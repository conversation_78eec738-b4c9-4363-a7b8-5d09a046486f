import express from "express";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    eliza<PERSON>ogger,
    generateImage,
    ModelClass,
} from "@elizaos/core";
import {
    generateUsingRecraft,
    generateImageToImageUsingRecraft,
    vectorizeImageUsingRecraft,
    removeBackgroundUsingRecraft,
} from "@elizaos/core/src/generation";
import { composeContext } from "@elizaos/core";
import { LinkedInClientInterface } from "agent-linkedin-client";
import TwitterClientInterface from "@elizaos/client-twitter";
import { generateMessageResponse } from "@elizaos/core";
import { Content, Memory } from "@elizaos/core";
import { stringToUuid } from "@elizaos/core";
import { getEmbeddingZeroVector } from "@elizaos/core";
import * as path from "path";
import { messageHandlerTemplate } from "../templates/message-templates";
import { imageStyleTemplates } from "../templates/image-styles";
import { saveBase64Image } from "../utils/file-utils";
import { CustomRequest } from "../models/types";

function handleImageGenerationError(error: any): {
    errorMessage: string;
    statusCode: number;
} {
    let errorMessage = "Image generation failed or returned no data.";
    let statusCode = 500;

    if (error) {
        elizaLogger.error("Image generation error:", error);

        if (
            error.status === 422 &&
            error.error?.error?.message?.includes("NSFW content")
        ) {
            errorMessage = "Description may contain NSFW content.";
            statusCode = 422;
        } else if (
            error.status === 429 ||
            (error.headers && error.headers["retry-after"])
        ) {
            errorMessage = `Rate limit exceeded. Please try again after few seconds.`;
            statusCode = 429;
        } else if (error.status && error.error?.error?.message) {
            errorMessage = `Image generation failed: ${error.error.error.message}`;
            statusCode = error.status;
        } else if (error.status) {
            errorMessage = `Image generation failed with status ${error.status}. Please try again.`;
            statusCode = error.status;
        }
    }

    return { errorMessage, statusCode };
}

/**
 * Handler for the /:agentId/whisper endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleWhisperRequest(
    agents: Map<string, AgentRuntime>,
    req: express.Request,
    res: express.Response
): Promise<void> {
    const customReq = req as any;
    const audioFile = customReq.file;
    const agentId = req.params.agentId;

    if (!audioFile) {
        res.status(400).send("No audio file provided");
        return;
    }

    let runtime = agents.get(agentId);

    // if runtime is null, look for runtime with the same name
    if (!runtime) {
        runtime = Array.from(agents.values()).find(
            (a) => a.character.name.toLowerCase() === agentId.toLowerCase()
        );
    }

    if (!runtime) {
        res.status(404).send("Agent not found");
        return;
    }

    const formData = new FormData();
    const audioBlob = new Blob([audioFile.buffer], {
        type: audioFile.mimetype,
    });
    formData.append("file", audioBlob, audioFile.originalname);
    formData.append("model", "whisper-1");

    const response = await fetch(
        "https://api.openai.com/v1/audio/transcriptions",
        {
            method: "POST",
            headers: {
                Authorization: `Bearer ${runtime.token}`,
            },
            body: formData,
        }
    );

    const data = await response.json();
    res.json(data);
}

/**
 * Handler for the /:agentId/message endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleMessageRequest(
    agents: Map<string, AgentRuntime>,
    req: express.Request,
    res: express.Response
): Promise<void> {
    const customReq = req as express.Request;
    const agentId = req.params.agentId;
    const roomId = stringToUuid(req.body.roomId ?? "default-room-" + agentId);
    const userId = stringToUuid(req.body.userId ?? "user");

    let runtime = agents.get(agentId);

    // if runtime is null, look for runtime with the same name
    if (!runtime) {
        runtime = Array.from(agents.values()).find(
            (a) => a.character.name.toLowerCase() === agentId.toLowerCase()
        );
    }

    if (!runtime) {
        res.status(404).send("Agent not found");
        return;
    }

    await runtime.ensureConnection(
        userId,
        roomId,
        req.body.userName,
        req.body.name,
        "direct"
    );

    const text = req.body.text;
    const messageId = stringToUuid(Date.now().toString());

    const attachments = [];
    if (customReq.file) {
        const filePath = path.join(
            process.cwd(),
            "agent",
            "data",
            "uploads",
            customReq.file.filename
        );
        attachments.push({
            id: Date.now().toString(),
            url: filePath,
            title: customReq.file.originalname,
            source: "direct",
            description: `Uploaded file: ${customReq.file.originalname}`,
            text: "",
            contentType: customReq.file.mimetype,
        });
    }

    const content: Content = {
        text,
        attachments,
        source: "direct",
        inReplyTo: undefined,
    };

    const userMessage = {
        content,
        userId,
        roomId,
        agentId: runtime.agentId,
    };

    const memory: Memory = {
        id: stringToUuid(messageId + "-" + userId),
        ...userMessage,
        agentId: runtime.agentId,
        userId,
        roomId,
        content,
        createdAt: Date.now(),
    };

    await runtime.messageManager.addEmbeddingToMemory(memory);
    await runtime.messageManager.createMemory(memory);

    let state = await runtime.composeState(userMessage, {
        agentName: runtime.character.name,
    });

    const context = composeContext({
        state,
        template: messageHandlerTemplate,
    });

    const response = await generateMessageResponse({
        runtime: runtime,
        context,
        modelClass: ModelClass.LARGE,
    });

    if (!response) {
        res.status(500).send("No response from generateMessageResponse");
        return;
    }

    // save response to memory
    const responseMessage: Memory = {
        id: stringToUuid(messageId + "-" + runtime.agentId),
        ...userMessage,
        userId: runtime.agentId,
        content: response,
        embedding: getEmbeddingZeroVector(),
        createdAt: Date.now(),
    };

    await runtime.messageManager.createMemory(responseMessage);

    state = await runtime.updateRecentMessageState(state);

    let message = null as Content | null;

    await runtime.processActions(
        memory,
        [responseMessage],
        state,
        async (newMessages) => {
            message = newMessages;
            return [memory];
        }
    );

    await runtime.evaluate(memory, state);

    // Check if we should suppress the initial message
    const action = runtime.actions.find((a) => a.name === response.action);
    const shouldSuppressInitialMessage = action?.suppressInitialMessage;

    if (!shouldSuppressInitialMessage) {
        if (message) {
            res.json([response, message]);
        } else {
            res.json([response]);
        }
    } else {
        if (message) {
            res.json([message]);
        } else {
            res.json([]);
        }
    }
}

/**
 * Handler for the /:agentId/image endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleImageRequest(
    agents: Map<string, AgentRuntime>,
    req: express.Request,
    res: express.Response
): Promise<void> {
    const agentId = req.params.agentId;
    const agent = agents.get(agentId);
    if (!agent) {
        res.status(404).send("Agent not found");
        return;
    }
    const description = req.body.description;
    const logoPrompt = description;

    const images = await generateImage(
        {
            prompt: logoPrompt,
            width: 512,
            height: 512,
            count: 1,
        },
        agent
    );
    res.json({ images });
}

/**
 * Handler for the /:agentId/post-image-gen endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleImageGenRequest(
    agents: Map<string, AgentRuntime>,
    req: express.Request,
    res: express.Response
): Promise<void> {
    const agentId = req.params.agentId;
    const planId = req.body.planId;
    const agent = agents.get(agentId);
    const imageStyle = req.body.style;
    const CONTENT = req.body.description;
    const width = req.body.width;
    const height = req.body.height;
    const steps = req.body.steps;
    const imageUrl = req.body.imageUrl;
    const modelNameFlux = req.body.modelNameFlux;
    const seed = req.body.seed;
    const guidanceScale = req.body.guidanceScale;
    if (!agent) {
        res.status(404).send("Agent not found");
        return;
    }

    if (!CONTENT) {
        res.status(400).json({
            error: "Description is required for image generation.",
        });
        return;
    }

    let imagePrompt = "";

    if (imageStyle?.length) {
        imageStyle.forEach((style: string) => {
            imagePrompt += imageStyleTemplates[style].getPromptInput();
        });

        const firstCommaIndex = imagePrompt.indexOf(",");
        if (firstCommaIndex !== -1) {
            imagePrompt = `${imagePrompt.substring(0, firstCommaIndex + 1)} ${CONTENT}, ${imagePrompt.substring(firstCommaIndex + 1)}`;
        } else {
            imagePrompt = `${imagePrompt}, ${CONTENT}`;
        }
    } else {
        imagePrompt = CONTENT;
    }
    const images = await generateImage(
        {
            prompt: imagePrompt,
            width: width || 1408,
            height: height || 1408,
            steps: steps,
            image_url: imageUrl || "",
            modelNameFlux: modelNameFlux || "",
            seed: seed,
            guidanceScale: guidanceScale,
            count: 1,
        },
        agent
    );

    if (images.success && images.data && images.data.length > 0) {
        elizaLogger.log(
            "Image generation successful, number of images:",
            images.data.length
        );
        const image = images.data[0];
        const filename = `${planId}_${Date.now()}`;
        const filepath = saveBase64Image(image, filename);

        res.json({ filepath });
    } else {
        const { errorMessage, statusCode } = handleImageGenerationError(
            images.error
        );
        res.status(statusCode).json({
            error: errorMessage,
        });
        elizaLogger.error("Image generation failed:", errorMessage);
    }
}

/**
 * Handler for the /:agentId/recraft-image endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleRecraftImageRequest(
    agents: Map<string, AgentRuntime>,
    req: express.Request,
    res: express.Response
): Promise<void> {
    const agentId = req.params.agentId;
    const agent = agents.get(agentId);

    if (!agent) {
        res.status(404).send("Agent not found");
        return;
    }

    const {
        prompt,
        width = 1408,
        height = 1408,
        count = 1,
        negativePrompt,
        style,
        substyle,
        model,
        seed,
        styleId,
        controls,
        text_layout,
    } = req.body;

    let imagePrompt = "";

    imagePrompt = prompt;

    if (!prompt) {
        res.status(400).json({
            error: "Prompt is required for Recraft image generation.",
        });
        return;
    }

    try {
        const result = await generateUsingRecraft({
            prompt: imagePrompt,
            width,
            height,
            count,
            negativePrompt,
            style,
            substyle,
            model,
            seed,
            styleId,
            controls,
            text_layout,
        });

        if (result.success && result.data && result.data.length > 0) {
            elizaLogger.log(
                "Recraft image generation successful, number of images:",
                result.data.length
            );
            res.json({
                success: true,
                images: result.data,
            });
        } else {
            const { errorMessage, statusCode } = handleImageGenerationError(
                result.error
            );
            res.status(statusCode).json({
                error: errorMessage,
            });
            elizaLogger.error("Recraft image generation failed:", errorMessage);
        }
    } catch (error) {
        elizaLogger.error("Error in Recraft image generation:", error);
        res.status(500).json({
            error: "Internal server error during image generation",
        });
    }
}

/**
 * Handler for the /:agentId/recraft-image-to-image endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleRecraftImageToImageRequest(
    agents: Map<string, AgentRuntime>,
    req: CustomRequest,
    res: express.Response
): Promise<void> {
    const agentId = req.params.agentId;
    const agent = agents.get(agentId);

    if (!agent) {
        res.status(404).send("Agent not found");
        return;
    }

    const imageFile = req.image;

    if (!imageFile) {
        res.status(400).json({
            error: "Image file is required for image-to-image generation.",
        });
        return;
    }

    const {
        prompt,
        strength = 0.8,
        style,
        substyle,
        model,
        negativePrompt,
        count = 1,
        controls,
    } = req.body;

    if (!prompt) {
        res.status(400).json({
            error: "Prompt is required for image-to-image generation.",
        });
        return;
    }

    try {
        const result = await generateImageToImageUsingRecraft({
            prompt,
            imageFile: imageFile.buffer,
            strength: parseFloat(strength),
            style,
            substyle,
            model,
            negativePrompt,
            count: parseInt(count),
            controls,
        });

        if (result.success && result.data && result.data.length > 0) {
            elizaLogger.log(
                "Recraft image-to-image generation successful, number of images:",
                result.data.length
            );
            res.json({
                success: true,
                images: result.data,
            });
        } else {
            const { errorMessage, statusCode } = handleImageGenerationError(
                result.error
            );
            res.status(statusCode).json({
                error: errorMessage,
            });
            elizaLogger.error(
                "Recraft image-to-image generation failed:",
                errorMessage
            );
        }
    } catch (error) {
        elizaLogger.error("Error in Recraft image-to-image generation:", error);
        res.status(500).json({
            error: "Internal server error during image-to-image generation",
        });
    }
}

/**
 * Handler for the /:agentId/recraft-vectorize endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleRecraftVectorizeRequest(
    agents: Map<string, AgentRuntime>,
    req: CustomRequest,
    res: express.Response
): Promise<void> {
    const agentId = req.params.agentId;
    const agent = agents.get(agentId);

    if (!agent) {
        res.status(404).send("Agent not found");
        return;
    }

    const imageFile = req.image;

    if (!imageFile) {
        res.status(400).json({
            error: "Image file is required for vectorization.",
        });
        return;
    }

    const { responseFormat = "url" } = req.body;

    try {
        const result = await vectorizeImageUsingRecraft({
            imageFile: imageFile.buffer,
            responseFormat,
        });

        if (result.success && result.data) {
            elizaLogger.log("Recraft vectorization successful");
            res.json({
                success: true,
                vectorImage: result.data,
            });
        } else {
            const { errorMessage, statusCode } = handleImageGenerationError(
                result.error
            );
            res.status(statusCode).json({
                error: errorMessage,
            });
            elizaLogger.error("Recraft vectorization failed:", errorMessage);
        }
    } catch (error) {
        elizaLogger.error("Error in Recraft vectorization:", error);
        res.status(500).json({
            error: "Internal server error during vectorization",
        });
    }
}

/**
 * Handler for the /:agentId/recraft-remove-background endpoint
 * @param agents Map of agent runtimes
 * @param req Express request
 * @param res Express response
 */
export async function handleRecraftRemoveBackgroundRequest(
    agents: Map<string, AgentRuntime>,
    req: CustomRequest,
    res: express.Response
): Promise<void> {
    const agentId = req.params.agentId;
    const agent = agents.get(agentId);

    if (!agent) {
        res.status(404).send("Agent not found");
        return;
    }

    const imageFile = req.file;

    if (!imageFile) {
        res.status(400).json({
            error: "Image file is required for background removal.",
        });
        return;
    }

    const { responseFormat = "url" } = req.body;
    console.log(imageFile);

    try {
        const result = await removeBackgroundUsingRecraft({
            imageFile: imageFile.buffer,
            responseFormat,
        });

        if (result.success && result.data) {
            elizaLogger.log("Recraft background removal successful");
            res.json({
                success: true,
                processedImage: result.data,
            });
        } else {
            const { errorMessage, statusCode } = handleImageGenerationError(
                result.error
            );
            res.status(statusCode).json({
                error: errorMessage,
            });
            elizaLogger.error(
                "Recraft background removal failed:",
                errorMessage
            );
        }
    } catch (error) {
        elizaLogger.error("Error in Recraft background removal:", error);
        res.status(500).json({
            error: "Internal server error during background removal",
        });
    }
}

export const handleStopAgent = async (
    agents: Map<string, AgentRuntime>,
    db: any,
    agentId: string,
    unregisterAgent: (runtime: AgentRuntime) => void,
    req: express.Request,
    res: express.Response
) => {
    try {
        const runtime: AgentRuntime = agents.get(agentId);
        if (runtime.clients.twitter) {
            await TwitterClientInterface.stop(runtime);
        }
        if (runtime.clients.linkedin) {
            await LinkedInClientInterface.stop(runtime);
        }
        unregisterAgent(agents.get(agentId));
        const rooms = await db.getRooms();
        const room = rooms.find((r: any) => r.id === agentId);
        await db.updateRoomStatus(
            agentId,
            "stopped",
            room.character,
            room.settings
        );
        res.status(200).json({ success: true });
    } catch (error) {
        console.log(error);
        res.status(400).json({ success: false });
    }
};

/**
 * Handler for updating LinkedIn business account setting
 * @param agents Map of agent runtimes
 * @param db Database instance
 * @param req Express request
 * @param res Express response
 */
export const handleUpdateLinkedInBusinessAccount = async (
    agents: Map<string, AgentRuntime>,
    db: any,
    req: express.Request,
    res: express.Response
): Promise<void> => {
    try {
        const agentId = req.params.agentId;
        const { businessAccount } = req.body;

        // Validate input
        if (typeof businessAccount !== "boolean") {
            res.status(400).json({
                success: false,
                message: "businessAccount must be a boolean value",
            });
            return;
        }

        // Get the agent runtime
        let runtime = agents.get(agentId);
        if (!runtime) {
            runtime = Array.from(agents.values()).find(
                (a) => a.character.name.toLowerCase() === agentId.toLowerCase()
            );
        }

        if (!runtime) {
            res.status(404).json({
                success: false,
                message: "Agent not found",
            });
            return;
        }

        // Check if LinkedIn client exists
        const linkedInManager = runtime.clients.linkedin;
        if (!linkedInManager) {
            res.status(400).json({
                success: false,
                message: "LinkedIn client not found for this agent",
            });
            return;
        }

        // Update the business account property
        linkedInManager.client.businessAccount = businessAccount;

        // Get current room settings to preserve other settings
        const rooms = await db.getRooms();
        const room = rooms.find((r: any) => r.id === agentId);

        let currentSettings = {};
        if (room && room.settings) {
            try {
                currentSettings =
                    typeof room.settings === "string"
                        ? JSON.parse(room.settings)
                        : room.settings;
            } catch (error) {
                elizaLogger.error(
                    "Failed to parse current room settings:",
                    error
                );
            }
        }

        // Update settings with new business account value
        const updatedSettings = {
            ...currentSettings,
            businessAccount,
        };

        // Update room settings in database
        await db.updateRoomStatus(
            runtime.agentId,
            room?.status || "active",
            room?.character,
            updatedSettings
        );

        elizaLogger.log(
            `LinkedIn business account updated for agent ${agentId}: ${businessAccount}`
        );

        res.status(200).json({
            success: true,
            message: "LinkedIn account setting updated successfully",
        });
    } catch (error) {
        elizaLogger.error("Error updating LinkedIn account:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

// might be useful later // /services/image-service.ts
// import { generateImage, generateText } from "@elizaos/core";
// import { imageStyleTemplates } from "../config/image-styles";
// import { saveBase64Image } from "../utils/file-utils";

// /**
//  * Generate an image based on content and style
//  * @param content The content to generate an image for
//  * @param style The style to use
//  * @returns The generated image data
//  */
// export async function generateImageForContent(
//     content: string,
//     style: string = "photorealistic"
// ): Promise<{ imageUrl: string; localPath: string }> {
//     // Get the style template
//     const styleTemplate = imageStyleTemplates[style] || imageStyleTemplates.photorealistic;

//     // Generate the prompt
//     const promptInput = styleTemplate.getPromptInput(content);

//     // Generate the prompt text
//     const promptResponse = await generateText({
//         model: "claude-3-haiku-20240307",
//         systemPrompt: styleTemplate.systemPrompt,
//         prompt: promptInput,
//         temperature: 0.7,
//         maxTokens: 500,
//     });

//     // Generate the image
//     const imageResponse = await generateImage({
//         model: "dall-e-3",
//         prompt: promptResponse.text,
//         n: 1,
//         size: "1024x1024",
//     });

//     // Save the image
//     const filename = `image_${Date.now()}`;
//     const localPath = saveBase64Image(imageResponse.data[0].url, filename);

//     return {
//         imageUrl: imageResponse.data[0].url,
//         localPath,
//     };
// }
