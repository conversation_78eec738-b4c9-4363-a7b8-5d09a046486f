const agentBaseUrl =
  process.env.NEXT_PUBLIC_AGENT_URL || "http://localhost:2151";

export const pinataUploadUrl = "/api/pinata";
export const categoriesUrl = "/api/categories";
export const revalidateUrl = "/api/revalidate";
export const startTwitterAgentUrl = `${agentBaseUrl}/start-twitter-agent`;
export const startTwitterBotAgentUrl = `${agentBaseUrl}/start-twitter-bot-agent`;
export const startLinkedInAgentUrl = `${agentBaseUrl}/start-linkedin-agent`;
export const startInstagramAgentUrl = `${agentBaseUrl}/start-instagram-agent`;
export const updateCharacterUrl = `${agentBaseUrl}/agents/%agentId%/set`;
export const updateTweetUrl = `${agentBaseUrl}/agents/%agentId%/update-tweet`;
export const updateTweetImageUrl = `${agentBaseUrl}/agents/%agentId%/update-tweet-image`;
export const deleteTweetImageUrl = `${agentBaseUrl}/agents/%agentId%/remove-tweet-image`;
export const fetchNewTweetsUrl = `${agentBaseUrl}/agents/%agentId%/new-tweets`;
export const fetchAgentSettingsUrl = `${agentBaseUrl}/agents/%agentId%`;
export const fetchTweetsUrl = `${agentBaseUrl}/agents/%agentId%/posts`;
export const schedulePostUrl = `${agentBaseUrl}/agents/%agentId%/schedule-post`;
export const updatePostUrl = `${agentBaseUrl}/agents/%agentId%/update-post`;
export const deletePostUrl = `${agentBaseUrl}/agents/%agentId%/delete-post`;
export const stopAgentUrl = `${agentBaseUrl}/stop-agent/%agentId%`;
export const knowledgeBaseUploadUrl = `${agentBaseUrl}/files/upload`;
export const getAllAgentsUrl = `${agentBaseUrl}/agents`;
export const toggleLinkedInAccountUrl = `${agentBaseUrl}/agents/%agentId%/toggle-linkedin-account`;
export const checkTwitterPremiumUrl = `${agentBaseUrl}/check-twitter-premium`;
export const removeBackgroundUrl = `${agentBaseUrl}/%agentId%/recraft-remove-background`;
export const stripeSessionUrl = "/api/get-session?session_id=%sessionId%";
export const twitterAuthUrl = '/api/twitter/auth'